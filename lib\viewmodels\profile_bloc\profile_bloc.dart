import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/utils/image_picker_utils.dart';
import 'package:room_eight/core/utils/constants.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';
import 'package:room_eight/models/location_model/location_model.dart';
import 'package:room_eight/models/setting_model/confirm_delete_account_model.dart';
import 'package:room_eight/models/setting_model/delete_account_model.dart';
import 'package:room_eight/models/user_profile_model/user_profile_model.dart';
import 'package:room_eight/repository/profile_repository/profile_repository.dart';

part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  Timer? _timer;
  final ProfileRepository profileRepository;

  ProfileBloc(this.profileRepository) : super(ProfileState.initial()) {
    // Form Fields
    on<FullProfileNameChanged>(_onFullNameChanged);
    on<NameChanged>(_onNameChanged);
    on<EmailChanged>(_onEmailChanged);
    on<ProfileDateOfBirthChanged>(_onDateOfBirthChanged);
    on<GenderChanged>(_onGenderChanged);
    on<PreferredGenderChanged>(_onPreferredGenderChanged);
    on<SmokingPreferenceChanged>(_onSmokingPreferenceChanged);
    on<CleanlinessChanged>(_onCleanlinessChanged);
    on<PetPreferenceChanged>(_onPetPreferenceChanged);
    on<ClassStandingChanged>(_onClassStandingChanged);
    on<AgeChanged>(_onAgeChanged);
    on<DobChanged>(_onDobChanged);
    on<LeasePeriodChanged>(_onLeasePeriodChanged);
    on<ContactNumberChanged>(_onContactNumberChanged);
    on<AboutChanged>(_onAboutChanged);
    on<SelectPreferredLocations>(_onSelectPreferredLocations);
    on<AddLocation>(_onAddLocation);
    on<RemoveLocation>(_onRemoveLocation);

    // Profile submission
    on<ProfileSubmitted>(_onProfileSubmitted);
    on<FinalProfileSubmitted>(_onFinalProfileSubmitted);

    //Get User Profile
    on<GetUserProfile>(_onGetUserProfile);

    //Edit Profile
    on<EditProfile>(_onEditProfile);

    // Photos
    on<SelectUserProfile>(_onSelectUserProfile);
    on<ProfileImageChanged>(_onProfileImageChanged);
    on<AddPhoto>(_onAddPhoto);
    on<RemovePhoto>(_onRemovePhoto);
    on<SelectMultiplePhotos>(_onSelectMultiplePhotos);

    // Personality Tags
    on<AddPersonalityTag>(_onAddPersonalityTag);
    on<RemovePersonalityTag>(_onRemovePersonalityTag);
    on<AddCustomPersonalityTag>(_onAddCustomPersonalityTag);

    // Selection Events
    on<SelectGender>(_onSelectGender);
    on<SelectPreferredGender>(_onSelectPreferredGender);
    on<SelectPeriod>(_onSelectPeriod);
    on<SelectSmokingPerson>(_onSelectSmokingPerson);
    on<SelectCleanLevel>(_onSelectCleanLevel);
    on<SelectPet>(_onSelectPet);
    on<SelectClassStand>(_onSelectClassStand);
    on<SelectHabitsAndLifestyle>(_onSelectHabitsAndLifestyle);
    on<SelectCleanlinessLivingStyle>(_onSelectCleanlinessLivingStyle);
    on<SelectInterestsHobbies>(_onSelectInterestsHobbies);
    on<TogglePickThings>(_onTogglePickThings);

    // Misc
    on<SearchChanged>(_onSearchChanged);
    on<SaveProfile>(_onSaveProfile);

    on<GetSelectionOption>(_onGetSelectionOption);

    on<DeleteAccount>(_onDeleteAccount);
    on<ConfirmDeleteAccount>(_onConfirmDeleteAccount);
    on<OnOtpChange>(_onOnOtpChange);
  }

  // ========== Event Handlers ==========

  Future<void> _onProfileSubmitted(
    ProfileSubmitted event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));
      Logger.lOG("ok");
      NavigatorService.pushNamed(AppRoutes.addPersonalDetailScreen2);
      emit(state.copyWith(isloginLoading: false));
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isloginLoading: false));
    }
  }

  Future<void> _onGetUserProfile(
    GetUserProfile event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(state.copyWith(isGetUserProfileLoading: true));

      UserProfileResponse userProfileResponse = await profileRepository
          .userProfileCall();

      Logger.lOG(
        "Get User Profile successful: ${userProfileResponse.toString()}",
      );

      if (userProfileResponse.status == true &&
          userProfileResponse.data != null) {
        final userData = userProfileResponse.data!;

        // Update all controllers with fetched data
        state.nameController.text = userData.fullName ?? "";
        state.fullNameController?.text = userData.fullName ?? "";
        state.dobController?.text = userData.dob ?? "";
        state.contactNumberController.text = userData.contactNumber ?? "";
        state.aboutController.text = userData.about ?? "";

        // Calculate age from date of birth
        final age = _calculateAgeFromDob(userData.dob ?? "");
        state.ageController.text = age.toString();

        // Convert API values back to UI values using constants
        final genderFromApi =
            _getKeyFromValue(genderIdMap, userData.gender ?? "1") ?? "";
        final preferredGenderFromApi =
            _getKeyFromValue(genderIdMap, userData.preferedGender ?? "0") ?? "";
        final smokingFromApi =
            _getKeyFromValue(smokingIdMap, userData.preferedSmoking ?? "0") ??
            "";
        final cleanlinessFromApi =
            _getKeyFromValue(cleanlinessIdMap, userData.cleaniness ?? "0") ??
            "";
        final petFromApi =
            _getKeyFromValue(petBoolMap, userData.isHavingPet ?? false) ?? "";
        final classStandingFromApi =
            _getKeyFromValue(
              classStandingIdMap,
              userData.classStanding ?? "0",
            ) ??
            "";
        final leasePeriodFromApi =
            _getKeyFromValue(
              leasePeriodDays,
              int.tryParse(userData.preferedLeasePeriod ?? "0") ?? 270,
            ) ??
            "";

        state.leasePeriodController.text = leasePeriodFromApi;

        // Convert LatLong list to LocationModel list
        final List<LocationModel> convertedLocations =
            _convertLatLongToLocationModel(userData.preferedLocations ?? []);

        emit(
          state.copyWith(
            isGetUserProfileLoading: false,
            nameController: state.nameController,
            fullNameController: state.fullNameController,
            dobController: state.dobController,
            ageController: state.ageController,
            contactNumberController: state.contactNumberController,
            aboutController: state.aboutController,
            selectedGender: genderFromApi,
            selectedPreferredGender: preferredGenderFromApi,
            selectedSmokingPerson: smokingFromApi,
            sleectedCleanLevenl: cleanlinessFromApi,
            selectedPet: petFromApi,
            selectedClassStand: classStandingFromApi,
            selectedPeriod: leasePeriodFromApi,
            leasePeriodController: state.leasePeriodController,
            profileImagePath: userData.profilePicture ?? "",
            photoPaths:
                userData.profilePictures
                    ?.map((pic) => pic.url ?? '')
                    .toList() ??
                [],
            originalPhotoPaths:
                userData.profilePictures
                    ?.map((pic) => pic.url ?? '')
                    .toList() ??
                [],
            originalProfilePictures: userData.profilePictures ?? [],
            removedPhotoPaths: [], // Initialize empty list for removed photos
            selectedHabitsAndLifestyle: userData.habitsLifestyle ?? [],
            selectedCleanlinessLivingStyle: userData.livingStyle ?? [],
            selectedInterestsHobbies: userData.interestsHobbies ?? [],
            selectedLocations: convertedLocations,
          ),
        );
      } else {
        Logger.lOG("Get User Profile failed: ${userProfileResponse.message}");
      }
    } catch (e) {
      Logger.lOG("Get User Profile error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isGetUserProfileLoading: false));
    }
  }

  Future<void> _onEditProfile(
    EditProfile event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));

      // Handle profile picture
      String profilePicturePath;
      if (state.userProfile != null && state.userProfile!.path.isNotEmpty) {
        // User has selected a new image
        profilePicturePath = state.userProfile!.path;
        Logger.lOG("Using newly selected profile image: $profilePicturePath");
      } else {
        // No new profile image selected, don't send profile picture
        profilePicturePath = '';
        Logger.lOG("No new profile image selected");
      }

      // Handle multiple photos - only process newly added photos (not in originalPhotoPaths)
      List<String> newPhotoPaths = state.photoPaths
          .where((photoPath) => !state.originalPhotoPaths.contains(photoPath))
          .toList();

      List<String> profilePictures = [];

      // If no new photos were added, don't send any photos to the API
      if (newPhotoPaths.isEmpty) {
        Logger.lOG("No new photos added, skipping photo processing");
      } else {
        Logger.lOG("Processing ${newPhotoPaths.length} newly added photos");
        // Since we're only dealing with newly added photos, they should be local file paths
        profilePictures = newPhotoPaths;
        for (String photoPath in newPhotoPaths) {
          Logger.lOG("New photo path: $photoPath");
        }
      }
      final fullName = state.nameController.text.trim();
      final dob = state.dobController?.text.trim() ?? "";
      final gender = genderIdMap[state.selectedGender] ?? "1";
      final preferredGender = genderIdMap[state.selectedPreferredGender] ?? "1";
      final preferredSmoking = smokingIdMap[state.selectedSmokingPerson] ?? "4";
      final cleanliness = cleanlinessIdMap[state.sleectedCleanLevenl] ?? "3";
      final isHavingPet = petBoolMap[state.selectedPet] ?? true;
      final classStanding = classStandingIdMap[state.selectedClassStand] ?? "5";
      final habitsLifestyle = "[${state.selectedHabitsAndLifestyle.join(',')}]";
      final livingStyle = "[${state.selectedCleanlinessLivingStyle.join(',')}]";
      final interestsHobbies = "[${state.selectedInterestsHobbies.join(',')}]";
      final about = state.aboutController.text;
      final contactNumber = state.contactNumberController.text.trim();
      final preferredLeasePeriod =
          leasePeriodDays[state.selectedPeriod]?.toString() ?? "0";
      final preferredLocations = _buildLocationString();
      final personalityTypeDescription = state.personalityTags.join(", ");

      // // Log final data being sent to API
      // Logger.lOG("=== Final Edit Profile Data ===");
      // Logger.lOG("Profile Picture Path: $profilePicturePath");
      // Logger.lOG("Profile Pictures Count: ${profilePictures.length}");
      // for (int i = 0; i < profilePictures.length; i++) {
      //   Logger.lOG("Profile Picture $i: ${profilePictures[i]}");
      // }
      // Logger.lOG("Full Name: $fullName");
      // Logger.lOG("DOB: $dob");
      // Logger.lOG("================================");

      // Logger.lOG("Removed photos count: ${state.removedPhotoPaths.length}");
      // for (int i = 0; i < state.removedPhotoPaths.length; i++) {
      //   Logger.lOG("Removed photo $i: '${state.removedPhotoPaths[i]}'");
      // }

      final response = await profileRepository.editProfileCall(
        profilePicturePath: profilePicturePath,
        profilePictures: profilePictures,
        removedPhotoPaths: state.removedPhotoPaths,
        removedPhotoIds: _getRemovedPhotoIds(),
        fullName: fullName,
        dob: dob,
        gender: gender,
        preferredGender: preferredGender,
        preferredSmoking: preferredSmoking,
        cleanliness: cleanliness,
        isHavingPet: isHavingPet,
        classStanding: classStanding,
        habitsLifestyle: habitsLifestyle,
        livingStyle: livingStyle,
        interestsHobbies: interestsHobbies,
        about: about,
        contactNumber: contactNumber,
        preferredLeasePeriod: preferredLeasePeriod,
        preferredLocations: preferredLocations,
        personalityTypeDescription: personalityTypeDescription,
      );

      if (response.status) {
        Logger.lOG("Profile updated successfully");
        NavigatorService.goBack();
        // Optionally show success message
      } else {
        Logger.lOG("Profile update failed: ${response.message}");
      }
    } catch (e) {
      Logger.lOG("Profile update error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isloginLoading: false));
    }
  }

  Future<void> _onFinalProfileSubmitted(
    FinalProfileSubmitted event,
    Emitter<ProfileState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));
      final profilePicturePath = state.userProfile?.path ?? "";
      final profilePictures = state.photoPaths;
      final fullName = state.fullNameController?.text.trim() ?? "";
      final dob = state.dobController?.text.trim() ?? "";
      final gender = genderIdMap[state.selectedGender] ?? "1";
      final preferredGender = genderIdMap[state.selectedPreferredGender] ?? "1";
      final preferredSmoking = smokingIdMap[state.selectedSmokingPerson] ?? "4";
      final cleanliness = cleanlinessIdMap[state.sleectedCleanLevenl] ?? "3";
      final isHavingPet = petBoolMap[state.selectedPet] ?? true;
      final classStanding = classStandingIdMap[state.selectedClassStand] ?? "5";
      final habitsLifestyle = "[${state.selectedHabitsAndLifestyle.join(',')}]";
      final livingStyle = "[${state.selectedCleanlinessLivingStyle.join(',')}]";
      final interestsHobbies = "[${state.selectedInterestsHobbies.join(',')}]";
      final aboutText = state.aboutController.text.trim();
      final contactNumber = state.contactNumberController.text.trim();
      final preferredLeasePeriod =
          leasePeriodDays[state.selectedPeriod]?.toString() ?? "360";
      final preferredLocations = _buildLocationString();
      const personalityTypeDescription =
          "Friendly, Organized, Quiet, Outgoing, Clean, Adventurous";

      final response = await profileRepository.profileDetailCall(
        profilePicturePath: profilePicturePath,
        profilePictures: profilePictures,
        fullName: fullName,
        dob: dob,
        gender: gender,
        preferredGender: preferredGender,
        preferredSmoking: preferredSmoking,
        cleanliness: cleanliness,
        isHavingPet: isHavingPet,
        classStanding: classStanding,
        habitsLifestyle: habitsLifestyle,
        livingStyle: livingStyle,
        interestsHobbies: interestsHobbies,
        about: aboutText,
        contactNumber: contactNumber,
        preferredLeasePeriod: preferredLeasePeriod,
        preferredLocations: preferredLocations,
        personalityTypeDescription: personalityTypeDescription,
      );

      if (response.status) {
        Logger.lOG("Profile submitted successfully");
        Prefobj.preferences?.put(Prefkeys.IS_LOGIN, true);
        NavigatorService.pushNamed(AppRoutes.roomEightNavBar);
      } else {
        Logger.lOG("Profile submission failed: ${response.message}");
      }
    } catch (e) {
      Logger.lOG("Profile submission error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isloginLoading: false));
    }
  }

  void _onFullNameChanged(
    FullProfileNameChanged event,
    Emitter<ProfileState> emit,
  ) {
    state.fullNameController?.text = event.fullName;
    emit(state.copyWith(fullNameController: state.fullNameController));
  }

  void _onNameChanged(NameChanged event, Emitter<ProfileState> emit) {
    state.nameController.text = event.name;
    emit(state.copyWith(nameController: state.nameController));
  }

  void _onEmailChanged(EmailChanged event, Emitter<ProfileState> emit) {
    state.emailController.text = event.email;
    emit(state.copyWith(emailController: state.emailController));
  }

  void _onDateOfBirthChanged(
    ProfileDateOfBirthChanged event,
    Emitter<ProfileState> emit,
  ) {
    Logger.lOG("in bloc : ${state.dobController?.text}");
    Logger.lOG("in event : ${event.dateOfBirth}");
    state.dobController?.text = event.dateOfBirth;
    Logger.lOG("in after bloc : ${state.dobController?.text}");
    emit(state.copyWith(dobController: state.dobController));
    Logger.lOG("in bloc : ${state.dobController?.text}");
  }

  void _onGenderChanged(GenderChanged event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedGender: event.selectedGender));
  }

  void _onPreferredGenderChanged(
    PreferredGenderChanged event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedPreferredGender: event.preferredGender));
  }

  void _onSmokingPreferenceChanged(
    SmokingPreferenceChanged event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedSmokingPerson: event.smokingPreference));
  }

  void _onCleanlinessChanged(
    CleanlinessChanged event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(sleectedCleanLevenl: event.cleanliness));
  }

  void _onPetPreferenceChanged(
    PetPreferenceChanged event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedPet: event.petPreference));
  }

  void _onClassStandingChanged(
    ClassStandingChanged event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedClassStand: event.classStanding));
  }

  void _onAgeChanged(AgeChanged event, Emitter<ProfileState> emit) {
    emit(state.copyWith(ageController: TextEditingController(text: event.age)));
  }

  void _onDobChanged(DobChanged event, Emitter<ProfileState> emit) {
    // Format the date as needed for the API (e.g., "dd/MM/yyyy")
    final formattedDate =
        "${event.dob.day.toString().padLeft(2, '0')}/"
        "${event.dob.month.toString().padLeft(2, '0')}/"
        "${event.dob.year}";

    // Calculate and update age
    final age = _calculateAgeFromDob(formattedDate);

    emit(
      state.copyWith(
        dobController: TextEditingController(text: formattedDate),
        ageController: TextEditingController(text: age.toString()),
      ),
    );
  }

  void _onLeasePeriodChanged(
    LeasePeriodChanged event,
    Emitter<ProfileState> emit,
  ) {
    state.leasePeriodController.text = event.leasePeriod;

    emit(
      state.copyWith(
        selectedPeriod: event.leasePeriod,
        leasePeriodController: state.leasePeriodController,
      ),
    );
  }

  void _onContactNumberChanged(
    ContactNumberChanged event,
    Emitter<ProfileState> emit,
  ) {
    emit(
      state.copyWith(contactNumberController: state.contactNumberController),
    );
  }

  void _onAboutChanged(AboutChanged event, Emitter<ProfileState> emit) {
    emit(state.copyWith(aboutController: state.aboutController));
  }

  void _onSelectPreferredLocations(
    SelectPreferredLocations event,
    Emitter<ProfileState> emit,
  ) {
    state.preferredLocationsController.text = event.locations;
    emit(
      state.copyWith(
        preferredLocationsController: state.preferredLocationsController,
      ),
    );
  }

  void _onAddLocation(AddLocation event, Emitter<ProfileState> emit) {
    final updatedLocations = List<LocationModel>.from(state.selectedLocations)
      ..add(event.location);
    emit(state.copyWith(selectedLocations: updatedLocations));
  }

  void _onRemoveLocation(RemoveLocation event, Emitter<ProfileState> emit) {
    final updatedLocations = List<LocationModel>.from(state.selectedLocations)
      ..remove(event.location);
    emit(state.copyWith(selectedLocations: updatedLocations));
  }

  String _buildLocationString() {
    if (state.selectedLocations.isEmpty) {
      return "[{lat : 0, long: 0}]";
    }

    final locationList = state.selectedLocations
        .map(
          (location) =>
              "{lat : ${location.latitude}, long: ${location.longitude}}",
        )
        .join(',');
    return "[$locationList]";
  }

  List<int> _getRemovedPhotoIds() {
    final removedIds = <int>[];
    for (String removedUrl in state.removedPhotoPaths) {
      // Find the ProfilePicture object that matches this URL
      final matchingPicture = state.originalProfilePictures.firstWhere(
        (pic) => pic.url == removedUrl,
        orElse: () => ProfilePicture(id: null, url: null),
      );
      if (matchingPicture.id != null) {
        removedIds.add(matchingPicture.id!);
      }
    }
    return removedIds;
  }

  Future<void> _onSelectUserProfile(
    SelectUserProfile event,
    Emitter<ProfileState> emit,
  ) async {
    File? pickedImage = await Imagepickerutils.pickImageFromGallery();
    if (pickedImage != null && pickedImage.path.isNotEmpty) {
      emit(state.copyWith(userProfile: pickedImage));
      Logger.lOG("Image picked");
    } else {
      Logger.lOG("Image not picked");
    }
  }

  void _onProfileImageChanged(
    ProfileImageChanged event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(profileImagePath: event.imagePath));
  }

  void _onAddPhoto(AddPhoto event, Emitter<ProfileState> emit) {
    final updatedPhotos = List<String>.from(state.photoPaths)
      ..add(event.photoPath);
    emit(state.copyWith(photoPaths: updatedPhotos));
  }

  void _onRemovePhoto(RemovePhoto event, Emitter<ProfileState> emit) {
    final updatedPhotos = List<String>.from(state.photoPaths)
      ..remove(event.photoPath);

    // Track removed photos only if they were originally from the server
    final updatedRemovedPhotos = List<String>.from(state.removedPhotoPaths);
    if (state.originalPhotoPaths.contains(event.photoPath)) {
      updatedRemovedPhotos.add(event.photoPath);
      Logger.lOG("Added to removed photos: ${event.photoPath}");
    }

    emit(
      state.copyWith(
        photoPaths: updatedPhotos,
        removedPhotoPaths: updatedRemovedPhotos,
      ),
    );
  }

  void _onSelectMultiplePhotos(
    SelectMultiplePhotos event,
    Emitter<ProfileState> emit,
  ) {
    // Add new photos to existing ones, avoiding duplicates
    final currentPhotos = List<String>.from(state.photoPaths);
    final newPhotos = event.photoPaths
        .where((path) => !currentPhotos.contains(path))
        .toList();
    final updatedPhotos = [...currentPhotos, ...newPhotos];
    emit(state.copyWith(photoPaths: updatedPhotos));
  }

  void _onAddPersonalityTag(
    AddPersonalityTag event,
    Emitter<ProfileState> emit,
  ) {
    final updatedTags = List<String>.from(state.personalityTags)
      ..add(event.tag);
    emit(state.copyWith(personalityTags: updatedTags));
  }

  void _onRemovePersonalityTag(
    RemovePersonalityTag event,
    Emitter<ProfileState> emit,
  ) {
    final updatedTags = List<String>.from(state.personalityTags)
      ..remove(event.tag);
    emit(state.copyWith(personalityTags: updatedTags));
  }

  void _onAddCustomPersonalityTag(
    AddCustomPersonalityTag event,
    Emitter<ProfileState> emit,
  ) {
    if (!state.customPersonalityTags.contains(event.tag)) {
      emit(
        state.copyWith(
          customPersonalityTags: List.from(state.customPersonalityTags)
            ..add(event.tag),
        ),
      );
    }
  }

  void _onSelectGender(SelectGender event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedGender: event.gender));
  }

  void _onSelectPreferredGender(
    SelectPreferredGender event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedPreferredGender: event.preferredGender));
  }

  void _onSelectPeriod(SelectPeriod event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedPeriod: event.period));
  }

  void _onSelectSmokingPerson(
    SelectSmokingPerson event,
    Emitter<ProfileState> emit,
  ) {
    emit(state.copyWith(selectedSmokingPerson: event.type));
  }

  void _onSelectCleanLevel(SelectCleanLevel event, Emitter<ProfileState> emit) {
    emit(state.copyWith(sleectedCleanLevenl: event.level));
  }

  void _onSelectPet(SelectPet event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedPet: event.pet));
  }

  void _onSelectClassStand(SelectClassStand event, Emitter<ProfileState> emit) {
    emit(state.copyWith(selectedClassStand: event.classStand));
  }

  void _onSelectHabitsAndLifestyle(
    SelectHabitsAndLifestyle event,
    Emitter<ProfileState> emit,
  ) {
    final selectedOption = state.habitsLifestyle.firstWhere(
      (option) => option.name == event.habitsAndLifestyle,
      orElse: () => ProfileOptionModel(id: 0, name: '', icon: ''),
    );

    final habits = List<int>.from(state.selectedHabitsAndLifestyle);
    if (habits.contains(selectedOption.id)) {
      habits.remove(selectedOption.id);
    } else {
      habits.add(selectedOption.id ?? 0);
    }
    final newCount =
        habits.length +
        state.selectedCleanlinessLivingStyle.length +
        state.selectedInterestsHobbies.length;
    emit(
      state.copyWith(
        selectedHabitsAndLifestyle: habits,
        selectedOption: newCount,
      ),
    );
  }

  void _onSelectCleanlinessLivingStyle(
    SelectCleanlinessLivingStyle event,
    Emitter<ProfileState> emit,
  ) {
    final selectedOption = state.livingStyle.firstWhere(
      (option) => option.name == event.cleanlinessLivingStyle,
      orElse: () => ProfileOptionModel(id: 0, name: '', icon: ''),
    );

    final list = List<int>.from(state.selectedCleanlinessLivingStyle);
    if (list.contains(selectedOption.id)) {
      list.remove(selectedOption.id);
    } else {
      list.add(selectedOption.id ?? 0);
    }
    final newCount =
        state.selectedHabitsAndLifestyle.length +
        list.length +
        state.selectedInterestsHobbies.length;
    emit(
      state.copyWith(
        selectedCleanlinessLivingStyle: list,
        selectedOption: newCount,
      ),
    );
  }

  void _onSelectInterestsHobbies(
    SelectInterestsHobbies event,
    Emitter<ProfileState> emit,
  ) {
    final selectedOption = state.interestsHobbies.firstWhere(
      (option) => option.name == event.interestsHobbies,
      orElse: () => ProfileOptionModel(id: 0, name: '', icon: ''),
    );

    final hobbies = List<int>.from(state.selectedInterestsHobbies);
    if (hobbies.contains(selectedOption.id)) {
      hobbies.remove(selectedOption.id);
    } else {
      hobbies.add(selectedOption.id ?? 0);
    }
    final newCount =
        state.selectedHabitsAndLifestyle.length +
        state.selectedCleanlinessLivingStyle.length +
        hobbies.length;
    emit(
      state.copyWith(
        selectedInterestsHobbies: hobbies,
        selectedOption: newCount,
      ),
    );
  }

  void _onTogglePickThings(TogglePickThings event, Emitter<ProfileState> emit) {
    emit(state.copyWith(isPickThings: !state.isPickThings));
  }

  void _onSearchChanged(SearchChanged event, Emitter<ProfileState> emit) {}

  void _onSaveProfile(SaveProfile event, Emitter<ProfileState> emit) {
    emit(state);
  }

  Future<void> _onGetSelectionOption(
    GetSelectionOption event,
    Emitter<ProfileState> emit,
  ) async {
    emit(state.copyWith(profileLoading: true));

    ProfileModel result = await profileRepository.getSelectionMenu();
    if (result.status == true) {
      final List<ProfileOptionModel> habitsLifestyle =
          result.data!.habitsLifestyle ?? [];
      final List<ProfileOptionModel> livingStyle =
          result.data!.livingStyle ?? [];
      final List<ProfileOptionModel> interestsHobbies =
          result.data!.interestsHobbies ?? [];
      emit(
        state.copyWith(
          habitsLifestyle: habitsLifestyle,
          livingStyle: livingStyle,
          interestsHobbies: interestsHobbies,
          profileLoading: false,
        ),
      );
    }
  }

  // Helper method to get key from value in a map
  String? _getKeyFromValue<T>(Map<String, T> map, T value) {
    for (var entry in map.entries) {
      if (entry.value == value) {
        return entry.key;
      }
    }
    return null;
  }

  // Helper method to calculate age from date of birth
  int _calculateAgeFromDob(String dobString) {
    if (dobString.isEmpty) return 0;

    try {
      // Parse the date string (assuming format: YYYY-MM-DD or DD/MM/YYYY)
      DateTime? dob;

      if (dobString.contains('-')) {
        // Format: YYYY-MM-DD
        dob = DateTime.tryParse(dobString);
      } else if (dobString.contains('/')) {
        // Format: DD/MM/YYYY
        final parts = dobString.split('/');
        if (parts.length == 3) {
          final day = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          final year = int.tryParse(parts[2]);
          if (day != null && month != null && year != null) {
            dob = DateTime(year, month, day);
          }
        }
      }

      if (dob == null) return 0;

      final now = DateTime.now();
      int age = now.year - dob.year;

      // Check if birthday hasn't occurred this year yet
      if (now.month < dob.month ||
          (now.month == dob.month && now.day < dob.day)) {
        age--;
      }

      return age > 0 ? age : 0;
    } catch (e) {
      Logger.lOG("Error calculating age from DOB: $e");
      return 0;
    }
  }

  /// Convert LatLong list from API to LocationModel list for UI
  List<LocationModel> _convertLatLongToLocationModel(
    List<LatLong> latLongList,
  ) {
    return latLongList.map((latLong) {
      return LocationModel(
        name: 'Saved Location',
        latitude: latLong.lat ?? 0.0,
        longitude: latLong.long ?? 0.0,
        address:
            'Lat: ${latLong.lat?.toStringAsFixed(6)}, Lng: ${latLong.long?.toStringAsFixed(6)}',
      );
    }).toList();
  }

  void _onDeleteAccount(DeleteAccount event, Emitter<ProfileState> emit) async {
    final Map<String, dynamic> data = {
      "email": Prefobj.preferences?.get(Prefkeys.USER_MAIL_ID),
    };
    final DeleteAccountModel result = await profileRepository.deleteAccount(
      data: data,
    );
    if (result.status == true) {}
  }

  void _onConfirmDeleteAccount(
    ConfirmDeleteAccount event,
    Emitter<ProfileState> emit,
  ) async {
    final Map<String, dynamic> data = {
      "email": Prefobj.preferences?.get(Prefkeys.USER_MAIL_ID),
      "otp": state.otpController.text,
    };
    final ConfirmDeleteAccountModel result = await profileRepository
        .confirmDeleteAccount(data: data);
    if (result.status == true) {
      Prefobj.preferences?.put(Prefkeys.IS_LOGIN, false);
      Prefobj.preferences?.put(Prefkeys.AUTHTOKEN, '');
      NavigatorService.pushNamedAndRemoveUntil(AppRoutes.loginScreen);
    } else {
      emit(state.copyWith(isLogout: false));
    }
  }

  void _onOnOtpChange(OnOtpChange event, Emitter<ProfileState> emit) {
    state.otpController.text = event.otp;
    emit(state.copyWith(otpController: state.otpController));
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
